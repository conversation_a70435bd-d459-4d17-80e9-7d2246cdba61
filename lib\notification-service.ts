// تم نقل استيراد prisma إلى API routes فقط

export interface NotificationRule {
  priority: 'عادي' | 'طاريء' | 'طاريء جدا';
  reminderTime: number; // بالدقائق
  escalationTime: number; // بالدقائق
}

// قواعد التذكير والتصعيد المحسنة
export const NOTIFICATION_RULES: NotificationRule[] = [
  {
    priority: 'عادي',
    reminderTime: 24 * 60, // 24 ساعة
    escalationTime: 48 * 60 // 48 ساعة
  },
  {
    priority: 'طاريء',
    reminderTime: 4 * 60, // 4 ساعات
    escalationTime: 8 * 60 // 8 ساعات
  },
  {
    priority: 'طاريء جدا',
    reminderTime: 60, // ساعة واحدة
    escalationTime: 2 * 60 // ساعتان
  }
];

// مستويات التصعيد
export interface EscalationLevel {
  level: number;
  name: string;
  timeThreshold: number; // بالدقائق
  targetRoles: string[];
  notificationMethod: 'system' | 'email' | 'sms' | 'all';
  isUrgent: boolean;
}

export const ESCALATION_LEVELS: Record<string, EscalationLevel[]> = {
  'عادي': [
    {
      level: 1,
      name: 'تذكير أولي',
      timeThreshold: 24 * 60, // 24 ساعة
      targetRoles: ['manager'],
      notificationMethod: 'system',
      isUrgent: false
    },
    {
      level: 2,
      name: 'تصعيد للإدارة العليا',
      timeThreshold: 48 * 60, // 48 ساعة
      targetRoles: ['admin'],
      notificationMethod: 'email',
      isUrgent: true
    }
  ],
  'طاريء': [
    {
      level: 1,
      name: 'تذكير عاجل',
      timeThreshold: 4 * 60, // 4 ساعات
      targetRoles: ['manager'],
      notificationMethod: 'system',
      isUrgent: true
    },
    {
      level: 2,
      name: 'تصعيد طارئ',
      timeThreshold: 8 * 60, // 8 ساعات
      targetRoles: ['admin'],
      notificationMethod: 'all',
      isUrgent: true
    }
  ],
  'طاريء جدا': [
    {
      level: 1,
      name: 'تنبيه فوري',
      timeThreshold: 60, // ساعة واحدة
      targetRoles: ['manager', 'admin'],
      notificationMethod: 'all',
      isUrgent: true
    },
    {
      level: 2,
      name: 'تصعيد حرج',
      timeThreshold: 2 * 60, // ساعتان
      targetRoles: ['admin'],
      notificationMethod: 'all',
      isUrgent: true
    }
  ]
};

export class NotificationService {
  // دوال مساعدة فقط - جميع عمليات قاعدة البيانات في API routes

  // عناوين الطلبات الجديدة
  static getNewRequestTitle(priority: string): string {
    switch (priority) {
      case 'طاريء جدا':
        return 'طلب طارئ جداً جديد! 🚨';
      case 'طاريء':
        return 'طلب طارئ جديد! ⚠️';
      default:
        return 'طلب جديد من الموظفين 📋';
    }
  }

  // نص وقت التأخير
  static getOverdueTimeText(priority: string): string {
    switch (priority) {
      case 'طاريء جدا':
        return 'ساعة واحدة';
      case 'طاريء':
        return '4 ساعات';
      default:
        return '24 ساعة';
    }
  }

  // عناوين التصعيد
  static getEscalationTitle(level: any, priority: string): string {
    const urgencyEmoji = priority === 'طاريء جدا' ? '🚨' : priority === 'طاريء' ? '⚠️' : '📋';
    return `${urgencyEmoji} ${level.name} - طلب ${priority}`;
  }

  // رسائل التصعيد
  static getEscalationMessage(level: any, request: any): string {
    const timeText = this.getTimeText(level.timeThreshold);
    return `الطلب ${request.requestNumber} من ${request.employeeName} معلق منذ ${timeText}. ${level.name} مطلوب.`;
  }

  // تحويل الدقائق إلى نص مقروء
  static getTimeText(minutes: number): string {
    if (minutes >= 24 * 60) {
      const days = Math.floor(minutes / (24 * 60));
      return `${days} يوم`;
    } else if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      return `${hours} ساعة`;
    } else {
      return `${minutes} دقيقة`;
    }
  }





  // عناوين التصعيد
  static getEscalationTitle(level: EscalationLevel, priority: string): string {
    const urgencyEmoji = priority === 'طاريء جدا' ? '🚨' : priority === 'طاريء' ? '⚠️' : '📋';
    return `${urgencyEmoji} ${level.name} - طلب ${priority}`;
  }

  // رسائل التصعيد
  static getEscalationMessage(level: EscalationLevel, request: any): string {
    const timeText = this.getTimeText(level.timeThreshold);
    return `الطلب ${request.requestNumber} من ${request.employeeName} معلق منذ ${timeText}. ${level.name} مطلوب.`;
  }

  // تحويل الدقائق إلى نص مقروء
  static getTimeText(minutes: number): string {
    if (minutes >= 24 * 60) {
      const days = Math.floor(minutes / (24 * 60));
      return `${days} يوم`;
    } else if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      return `${hours} ساعة`;
    } else {
      return `${minutes} دقيقة`;
    }
  }

  // إرسال إشعارات البريد الإلكتروني (مؤقت)
  static async sendEmailNotifications(users: any[], title: string, message: string, request: any) {
    // TODO: تنفيذ إرسال البريد الإلكتروني
    console.log(`إرسال بريد إلكتروني لـ ${users.length} مستخدم: ${title}`);
  }

  // إرسال إشعارات SMS (مؤقت)
  static async sendSMSNotifications(users: any[], title: string, request: any) {
    // TODO: تنفيذ إرسال SMS
    console.log(`إرسال SMS لـ ${users.length} مستخدم: ${title}`);
  }
}
